{"name": "may<PERSON>-ch<PERSON><PERSON>-portfolio", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "start": "node server.js", "start:dev": "NODE_ENV=development node server.js", "start:prod": "NODE_ENV=production node server.js", "lint": "eslint . -c config/eslint.config.js", "lint:fix": "eslint . -c config/eslint.config.js --fix", "format": "prettier --write .", "format:check": "prettier --check .", "test": "vitest --config config/vitest.config.ts", "test:ui": "vitest --config config/vitest.config.ts --ui", "test:run": "vitest --config config/vitest.config.ts run", "test:coverage": "vitest --config config/vitest.config.ts run --coverage", "test:watch": "vitest --config config/vitest.config.ts --watch", "test:server": "NODE_ENV=test node --test server.test.js", "type-check": "tsc --noEmit", "preview": "vite preview", "prepare": "husky", "deploy": "bash scripts/deployment/deploy.sh", "health-check": "bash scripts/utilities/health-check.sh", "docker:helper": "bash scripts/utilities/docker-helper.sh"}, "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "express": "^4.18.2", "framer-motion": "^11.1.15", "helmet": "^7.1.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-intersection-observer": "^9.8.1", "react-particles": "^2.12.2", "react-scroll": "^1.9.0", "react-type-animation": "^3.2.0", "redis": "^4.6.12", "tsparticles-slim": "^2.12.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-scroll": "^1.8.10", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^1.3.1", "@vitest/ui": "^1.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "husky": "^9.0.11", "jsdom": "^24.0.0", "lint-staged": "^15.2.2", "postcss": "^8.4.35", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^1.3.1"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint -c config/eslint.config.js --fix", "prettier --write"], "*.{json,css,md,html}": ["prettier --write"]}}