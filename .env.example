# Environment Configuration Example
# Copy this file to .env and update the values as needed

# Application Environment
NODE_ENV=development

# Application Configuration
VITE_APP_NAME=Mayur Chavhan Portfolio
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=DevOps Engineer & Cloud Architect Portfolio

# API Configuration (if needed in the future)
VITE_API_BASE_URL=https://api.mayurchavhan.com
VITE_API_TIMEOUT=10000

# Analytics (if needed)
VITE_GOOGLE_ANALYTICS_ID=
VITE_GOOGLE_TAG_MANAGER_ID=

# Social Media Links
VITE_GITHUB_URL=https://github.com/mayurchavhan
VITE_LINKEDIN_URL=https://linkedin.com/in/mayurchavhan
VITE_TWITTER_URL=https://twitter.com/mayurchavhan
VITE_EMAIL=<EMAIL>

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_CONTACT_FORM=true
VITE_ENABLE_BLOG=true
VITE_ENABLE_DARK_MODE=true

# Performance Configuration
VITE_ENABLE_PWA=false
VITE_ENABLE_SERVICE_WORKER=false

# Development Configuration
VITE_DEV_TOOLS=true
VITE_SHOW_PERFORMANCE_METRICS=false

# Build Configuration
VITE_BUILD_SOURCEMAP=true
VITE_BUILD_ANALYZE=false

# Security Configuration
VITE_CSP_ENABLED=true
VITE_SECURITY_HEADERS=true

# Monitoring (for production)
VITE_SENTRY_DSN=
VITE_SENTRY_ENVIRONMENT=development

# CDN Configuration (for production)
VITE_CDN_URL=
VITE_ASSETS_CDN_URL=

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_TTL_DEFAULT=1800
