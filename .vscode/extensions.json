{"recommendations": ["esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-typescript-next", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode-remote.remote-containers", "ms-vscode.test-adapter-converter", "vitest.explorer"], "unwantedRecommendations": ["ms-vscode.vscode-typescript"]}