# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript/TypeScript files
[*.{js,jsx,ts,tsx}]
indent_size = 2

# JSON files
[*.json]
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false

# Package.json
[package.json]
indent_size = 2

# Docker files
[{Dockerfile,Dockerfile.*}]
indent_size = 4

# Shell scripts
[*.sh]
indent_size = 4

# Python files (if any)
[*.py]
indent_size = 4

# CSS/SCSS files
[*.{css,scss,sass}]
indent_size = 2
