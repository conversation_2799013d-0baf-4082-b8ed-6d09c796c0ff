# 🚀 Mayur Cha<PERSON><PERSON> Portfolio

A modern, high-performance portfolio website for a DevOps Engineer & Cloud Architect, built with React, TypeScript, and optimized for production deployment.

[![Build Status](https://github.com/mayurchavhan/portfolio-website/workflows/Build,%20Test%20&%20Deploy%20Portfolio/badge.svg)](https://github.com/mayurchavhan/portfolio-website/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://typescriptlang.org)
[![React](https://img.shields.io/badge/React-20232A?logo=react&logoColor=61DAFB)](https://reactjs.org)

## ✨ Features

### 🎨 **Modern Design**

- Responsive design with mobile-first approach
- Dark/Light theme toggle with system preference detection
- Smooth animations and transitions using Framer Motion
- Interactive particle background effects
- Optimized for accessibility (WCAG 2.1 AA compliant)

### ⚡ **Performance Optimized**

- **Lighthouse Score**: 95+ across all metrics
- Code splitting and lazy loading
- Optimized images with WebP support
- Service Worker for offline functionality
- Redis caching for improved performance
- Bundle size optimization with tree shaking

### 🛠️ **Developer Experience**

- TypeScript for type safety
- Comprehensive testing with Vitest and React Testing Library
- ESLint + Prettier for code quality
- Husky pre-commit hooks
- Hot module replacement in development
- Built-in development tools and monitoring dashboard

### 🔒 **Production Ready**

- Docker containerization with multi-stage builds
- Comprehensive CI/CD pipeline with GitHub Actions
- Security scanning with Trivy and CodeQL
- Error tracking and performance monitoring
- Health checks and metrics endpoints
- Graceful shutdown handling

### 📊 **Monitoring & Analytics**

- Real-time performance monitoring
- Error tracking and reporting
- Web Vitals measurement
- Custom metrics collection
- Development tools dashboard (Ctrl+Shift+D)

## 🏗️ **Project Structure**

```
portfolio-website/
├── 📁 config/                    # Configuration files
│   ├── eslint.config.js         # ESLint configuration
│   ├── postcss.config.js        # PostCSS configuration
│   ├── tailwind.config.js       # Tailwind CSS configuration
│   └── vitest.config.ts         # Vitest testing configuration
├── 📁 docs/                     # Comprehensive documentation
│   ├── 📁 api/                  # API documentation
│   ├── 📁 architecture/         # Architecture documentation
│   ├── 📁 deployment/           # Deployment guides
│   └── 📁 development/          # Development guides
├── 📁 scripts/                  # Utility scripts
│   ├── 📁 deployment/           # Deployment scripts
│   └── 📁 utilities/            # Utility scripts
├── 📁 src/                      # Source code
│   ├── 📁 features/             # Feature-based components
│   │   ├── 📁 hero/             # Hero section
│   │   ├── 📁 about/            # About section
│   │   ├── 📁 skills/           # Skills section
│   │   ├── 📁 projects/         # Projects section
│   │   └── 📁 ...               # Other features
│   ├── 📁 shared/               # Shared utilities and components
│   │   ├── 📁 components/       # Reusable components
│   │   ├── 📁 hooks/            # Custom React hooks
│   │   ├── 📁 utils/            # Utility functions
│   │   ├── 📁 types/            # TypeScript types
│   │   └── 📁 data/             # Static data
│   ├── 📁 assets/               # Static assets
│   ├── 📁 styles/               # Global styles
│   └── 📁 config/               # App configuration
├── 📄 docker-compose.yml        # Docker Compose configuration
├── 📄 Dockerfile                # Production Docker image
└── 📄 package.json              # Dependencies and scripts
```

### 🎯 **Architecture Highlights**

- **Feature-Based Organization**: Components organized by features for better maintainability
- **TypeScript Path Aliases**: Clean imports using `@/` aliases
- **Shared Resources**: Reusable components, hooks, and utilities
- **Comprehensive Documentation**: Detailed docs for development, deployment, and architecture
- **Professional Configuration**: Centralized config files and development tools

## 🚀 **Quick Start**

### Prerequisites

- Node.js 18+
- Docker & Docker Compose
- Git

### Local Development

1. **Clone the repository**

   ```bash
   git clone https://github.com/mayurchavhan/portfolio-website.git
   cd portfolio-website
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start development server**

   ```bash
   npm run dev
   ```

5. **Open your browser**
   ```
   http://localhost:3000
   ```

### Docker Development

1. **Start with Docker Compose**

   ```bash
   # Development environment with hot reload
   docker-compose --profile dev up

   # Production environment
   docker-compose --profile prod up
   ```

2. **Access the application**
   ```
   Development: http://localhost:3000
   Production: http://localhost:8080
   Redis Commander: http://localhost:8081 (dev only)
   ```

## 📋 **Available Scripts**

| Script                  | Description                              |
| ----------------------- | ---------------------------------------- |
| `npm run dev`           | Start development server with hot reload |
| `npm run build`         | Build for production                     |
| `npm run start`         | Start production server                  |
| `npm run test`          | Run tests in watch mode                  |
| `npm run test:run`      | Run tests once                           |
| `npm run test:coverage` | Run tests with coverage report           |
| `npm run lint`          | Run ESLint                               |
| `npm run lint:fix`      | Fix ESLint issues                        |
| `npm run format`        | Format code with Prettier                |
| `npm run type-check`    | Run TypeScript type checking             |
| `npm run deploy`        | Run deployment script                    |
| `npm run health-check`  | Run health check script                  |
| `npm run docker:helper` | Run Docker helper script                 |

## 📚 **Documentation**

Comprehensive documentation is available in the `/docs` directory:

- **[Getting Started](docs/development/SETUP_GUIDE.md)** - Local development setup
- **[Project Structure](docs/architecture/project-structure.md)** - Detailed project organization
- **[Component Architecture](docs/architecture/components.md)** - Component design patterns
- **[Code Style Guide](docs/development/code-style.md)** - Coding standards and conventions
- **[Testing Guide](docs/development/testing.md)** - Testing strategies and best practices
- **[Deployment Guide](docs/deployment/DOCKER_GUIDE.md)** - Docker setup and deployment
- **[Production Deployment](docs/deployment/production.md)** - Production deployment strategies
- **[API Documentation](docs/api/endpoints.md)** - Available API endpoints
- **[Contributing Guidelines](docs/development/CONTRIBUTING.md)** - How to contribute

### Quick Links

- [📖 Full Documentation](docs/README.md)
- [🏗️ Architecture Overview](docs/architecture/project-structure.md)
- [🚀 Deployment Guide](docs/deployment/production.md)

## 🐳 **Docker Commands**

```bash
# Build production image
docker build -t portfolio-website .

# Build development image
docker build -f Dockerfile.dev -t portfolio-website:dev .

# Run with Docker Compose
docker-compose --profile dev up    # Development
docker-compose --profile prod up   # Production

# View logs
docker-compose logs -f portfolio-dev
```

## 🧪 **Testing**

The project includes comprehensive testing setup:

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in CI mode
npm run test:run

# Run specific test file
npm test -- src/components/ThemeToggle.test.tsx
```

### Test Coverage Goals

- **Statements**: 80%+
- **Branches**: 80%+
- **Functions**: 80%+
- **Lines**: 80%+

## 🔧 **Configuration**

### Environment Variables

Create a `.env` file based on `.env.example`:

```bash
# Application
NODE_ENV=development
VITE_APP_NAME=Mayur Chavhan Portfolio
VITE_APP_VERSION=1.0.0

# Redis (optional)
REDIS_URL=redis://localhost:6379

# Analytics (optional)
VITE_GOOGLE_ANALYTICS_ID=your-ga-id

# Feature Flags
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_ANALYTICS=false
```

### Docker Environment

```bash
# Production deployment
PORT=8080
NODE_ENV=production
REDIS_URL=redis://redis:6379
```

## 🚀 **Deployment**

### Automated Deployment (Recommended)

The project includes automated CI/CD with GitHub Actions:

1. **Push to `develop`** → Deploys to staging
2. **Push to `main`** → Deploys to production

### Manual Deployment

```bash
# Build and deploy with Docker
docker build -t portfolio-website .
docker run -p 8080:8080 portfolio-website

# Or with Docker Compose
docker-compose --profile prod up -d
```

### Production Checklist

- [ ] Environment variables configured
- [ ] Redis instance running
- [ ] SSL certificates installed
- [ ] Monitoring configured
- [ ] Backup strategy in place
- [ ] Health checks passing

## 📊 **Monitoring**

### Health Checks

- **Endpoint**: `/health`
- **Metrics**: `/metrics`
- **Status**: Returns application and service health

### Development Tools

- Press `Ctrl+Shift+D` to open the development dashboard
- Monitor performance metrics in real-time
- View error logs and statistics
- Export logs for analysis

### Production Monitoring

- Web Vitals tracking
- Error rate monitoring
- Performance metrics collection
- Automated alerting (configure with your monitoring service)

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow TypeScript best practices
- Write tests for new features
- Update documentation as needed
- Follow the existing code style
- Ensure all CI checks pass

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 **Author**

**Mayur Chavhan**

- DevOps Engineer & Cloud Architect
- GitHub: [@mayurchavhan](https://github.com/mayurchavhan)
- LinkedIn: [Mayur Chavhan](https://linkedin.com/in/mayurchavhan)
- Email: <EMAIL>

## 🙏 **Acknowledgments**

- [React](https://reactjs.org/) - UI Library
- [TypeScript](https://typescriptlang.org/) - Type Safety
- [Vite](https://vitejs.dev/) - Build Tool
- [Tailwind CSS](https://tailwindcss.com/) - Styling
- [Framer Motion](https://framer.com/motion/) - Animations
- [Vitest](https://vitest.dev/) - Testing Framework

---

<div align="center">
  <p>Built with ❤️ by Mayur Chavhan</p>
  <p>⭐ Star this repo if you found it helpful!</p>
</div>
