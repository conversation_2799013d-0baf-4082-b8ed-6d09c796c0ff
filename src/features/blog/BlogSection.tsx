import { blogPosts } from '@/shared/data/blog';
import { motion } from 'framer-motion';
import { ArrowRight, Clock } from 'lucide-react';
import React from 'react';
import { useInView } from 'react-intersection-observer';

const BlogSection: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 },
    },
  };

  return (
    <section id="blog" className="dark:bg-dark-100 bg-gray-50 py-20">
      <div className="container mx-auto px-4">
        <div className="mb-16 text-center">
          <h2 className="section-title inline-block">Latest Blog Posts</h2>
          <p className="mx-auto mt-8 max-w-2xl text-lg text-gray-600 dark:text-gray-400">
            I regularly write about DevOps practices, cloud technologies, and
            automation on my blog at
            <a
              href="https://techwhale.in"
              className="text-primary-600 dark:text-primary-400 mx-1 hover:underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              techwhale.in
            </a>
          </p>
        </div>

        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-2"
        >
          {blogPosts.map(post => (
            <motion.div
              key={post.id}
              variants={itemVariants}
              className="card group flex flex-col overflow-hidden"
            >
              <div className="relative h-48 overflow-hidden">
                <img
                  src={post.image}
                  alt={post.title}
                  className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute right-4 top-4">
                  <span className="bg-primary-600 rounded-full px-3 py-1 text-xs font-medium text-white">
                    {post.category}
                  </span>
                </div>
              </div>
              <div className="flex flex-grow flex-col p-6">
                <div className="mb-3 flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <span>{post.date}</span>
                  <span className="mx-2">•</span>
                  <span className="flex items-center">
                    <Clock size={14} className="mr-1" />
                    {post.readTime}
                  </span>
                </div>
                <h3 className="group-hover:text-primary-600 dark:group-hover:text-primary-400 mb-3 text-xl font-semibold text-gray-900 transition-colors dark:text-white">
                  {post.title}
                </h3>
                <p className="mb-4 flex-grow text-gray-600 dark:text-gray-400">
                  {post.excerpt}
                </p>
                <a
                  href={post.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary-600 dark:text-primary-400 mt-auto inline-flex items-center font-medium hover:underline"
                >
                  Read More
                  <ArrowRight size={16} className="ml-1" />
                </a>
              </div>
            </motion.div>
          ))}
        </motion.div>

        <div className="mt-12 text-center">
          <a
            href="https://techwhale.in"
            target="_blank"
            rel="noopener noreferrer"
            className="btn btn-outline"
          >
            View All Posts
          </a>
        </div>
      </div>
    </section>
  );
};

export default BlogSection;
