import React, { useState, useEffect } from 'react';
import { Settings, Activity, Bug, Database, Zap } from 'lucide-react';
import MonitoringDashboard from './MonitoringDashboard';
import { env } from '../config/env';
import { errorTracker } from '../utils/errorTracking';
import { performanceTracker } from '../utils/performance';

const DevTools: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showMonitoring, setShowMonitoring] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (env.DEV_TOOLS && !isInitialized) {
      initializeDevTools();
      setIsInitialized(true);
    }
  }, [isInitialized]);

  const initializeDevTools = async () => {
    try {
      // Initialize error tracking
      await errorTracker.initialize();
      
      // Initialize performance tracking
      await performanceTracker.initialize();
      
      console.log('🛠️ Dev Tools initialized');
    } catch (error) {
      console.error('Failed to initialize dev tools:', error);
    }
  };

  // Keyboard shortcut to toggle dev tools
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + D to toggle dev tools
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        setIsOpen(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Don't render in production unless explicitly enabled
  if (!env.DEV_TOOLS) {
    return null;
  }

  const triggerTestError = () => {
    try {
      throw new Error('Test error from DevTools');
    } catch (error) {
      errorTracker.captureError(
        error as Error,
        { component: 'DevTools', action: 'test-error' },
        'medium'
      );
    }
  };

  const clearCache = async () => {
    try {
      // Clear browser cache
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
      }
      
      // Clear localStorage
      localStorage.clear();
      
      // Clear sessionStorage
      sessionStorage.clear();
      
      console.log('✅ Cache cleared');
      alert('Cache cleared successfully!');
    } catch (error) {
      console.error('Failed to clear cache:', error);
      alert('Failed to clear cache');
    }
  };

  const exportLogs = () => {
    const logs = {
      timestamp: new Date().toISOString(),
      performance: performanceTracker.getPerformanceSummary(),
      errors: errorTracker.getErrorStats(),
      environment: {
        userAgent: navigator.userAgent,
        url: window.location.href,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
        connection: (navigator as any).connection ? {
          effectiveType: (navigator as any).connection.effectiveType,
          downlink: (navigator as any).connection.downlink,
        } : null,
      },
    };

    const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `portfolio-logs-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <>
      {/* Dev Tools Toggle Button */}
      <div className="fixed bottom-4 right-4 z-40">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-full shadow-lg transition-colors"
          title="Dev Tools (Ctrl+Shift+D)"
        >
          <Settings className="w-5 h-5" />
        </button>
      </div>

      {/* Dev Tools Panel */}
      {isOpen && (
        <div className="fixed bottom-20 right-4 bg-white dark:bg-dark-100 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 p-4 w-80 z-40">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Dev Tools
            </h3>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ✕
            </button>
          </div>

          <div className="space-y-3">
            {/* Monitoring Dashboard */}
            <button
              onClick={() => setShowMonitoring(true)}
              className="w-full flex items-center px-3 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors"
            >
              <Activity className="w-4 h-4 mr-2" />
              Performance Monitor
            </button>

            {/* Test Error */}
            <button
              onClick={triggerTestError}
              className="w-full flex items-center px-3 py-2 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 rounded hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors"
            >
              <Bug className="w-4 h-4 mr-2" />
              Trigger Test Error
            </button>

            {/* Clear Cache */}
            <button
              onClick={clearCache}
              className="w-full flex items-center px-3 py-2 bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200 rounded hover:bg-orange-200 dark:hover:bg-orange-900/50 transition-colors"
            >
              <Database className="w-4 h-4 mr-2" />
              Clear Cache
            </button>

            {/* Export Logs */}
            <button
              onClick={exportLogs}
              className="w-full flex items-center px-3 py-2 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 rounded hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors"
            >
              <Zap className="w-4 h-4 mr-2" />
              Export Logs
            </button>

            {/* Environment Info */}
            <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
              <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                <div>Environment: {env.NODE_ENV}</div>
                <div>Version: {env.APP_VERSION}</div>
                <div>Build: {import.meta.env.MODE}</div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
              <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">Quick Actions:</div>
              <div className="flex space-x-2">
                <button
                  onClick={() => window.location.reload()}
                  className="flex-1 px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  Reload
                </button>
                <button
                  onClick={() => console.clear()}
                  className="flex-1 px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  Clear Console
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Monitoring Dashboard Modal */}
      <MonitoringDashboard
        isVisible={showMonitoring}
        onClose={() => setShowMonitoring(false)}
      />
    </>
  );
};

export default DevTools;
