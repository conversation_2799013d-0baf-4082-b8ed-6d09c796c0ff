import React, { useState, useEffect } from 'react';
import { Activity, AlertTriangle, Clock, Database, Globe, Zap } from 'lucide-react';
import { performanceTracker } from '../utils/performance';
import { errorTracker } from '../utils/errorTracking';
import { cache } from '../utils/redis';
import { env } from '../config/env';

interface MonitoringDashboardProps {
  isVisible: boolean;
  onClose: () => void;
}

const MonitoringDashboard: React.FC<MonitoringDashboardProps> = ({ isVisible, onClose }) => {
  const [performanceData, setPerformanceData] = useState<any>(null);
  const [errorData, setErrorData] = useState<any>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isVisible) {
      fetchData();
      const interval = setInterval(fetchData, 5000); // Refresh every 5 seconds
      setRefreshInterval(interval);
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [isVisible]);

  const fetchData = async () => {
    try {
      // Get performance data
      const perfData = performanceTracker.getPerformanceSummary();
      setPerformanceData(perfData);

      // Get error data
      const errData = errorTracker.getErrorStats();
      setErrorData(errData);

      // Get cache stats
      const cacheData = await cache.getStats();
      setCacheStats(cacheData);
    } catch (error) {
      console.error('Failed to fetch monitoring data:', error);
    }
  };

  if (!isVisible || !env.DEV_TOOLS) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-dark-100 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-auto">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center">
            <Activity className="mr-2" />
            Performance Monitoring Dashboard
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ✕
          </button>
        </div>

        <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Web Vitals */}
          <div className="bg-gray-50 dark:bg-dark-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <Zap className="mr-2 text-yellow-500" />
              Web Vitals
            </h3>
            {performanceData?.webVitals ? (
              <div className="space-y-2">
                {Object.entries(performanceData.webVitals).map(([key, value]: [string, any]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">{key.toUpperCase()}:</span>
                    <span className="text-sm font-mono">
                      {value?.avg ? `${value.avg.toFixed(2)}ms` : 'N/A'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">Loading...</p>
            )}
          </div>

          {/* Error Statistics */}
          <div className="bg-gray-50 dark:bg-dark-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <AlertTriangle className="mr-2 text-red-500" />
              Error Statistics
            </h3>
            {errorData ? (
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Total Errors:</span>
                  <span className="text-sm font-mono">{errorData.totalErrors}</span>
                </div>
                {Object.entries(errorData.errorsBySeverity).map(([severity, count]: [string, any]) => (
                  <div key={severity} className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">{severity}:</span>
                    <span className="text-sm font-mono">{count}</span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">Loading...</p>
            )}
          </div>

          {/* Cache Statistics */}
          <div className="bg-gray-50 dark:bg-dark-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <Database className="mr-2 text-blue-500" />
              Cache Statistics
            </h3>
            {cacheStats ? (
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                  <span className="text-sm font-mono text-green-600">Connected</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Memory:</span>
                  <span className="text-sm font-mono">Available</span>
                </div>
              </div>
            ) : (
              <p className="text-sm text-gray-500">Redis not available</p>
            )}
          </div>

          {/* Resource Performance */}
          <div className="bg-gray-50 dark:bg-dark-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <Globe className="mr-2 text-green-500" />
              Resource Performance
            </h3>
            {performanceData?.resources ? (
              <div className="space-y-2">
                {Object.entries(performanceData.resources).map(([key, value]: [string, any]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">{key}:</span>
                    <span className="text-sm font-mono">
                      {value?.avg ? `${value.avg.toFixed(2)}ms` : 'N/A'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No data available</p>
            )}
          </div>

          {/* API Performance */}
          <div className="bg-gray-50 dark:bg-dark-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <Clock className="mr-2 text-purple-500" />
              API Performance
            </h3>
            {performanceData?.api ? (
              <div className="space-y-2">
                {Object.entries(performanceData.api).map(([key, value]: [string, any]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">{key}:</span>
                    <span className="text-sm font-mono">
                      {value?.avg ? `${value.avg.toFixed(2)}ms` : 'N/A'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No API calls recorded</p>
            )}
          </div>

          {/* Memory Usage */}
          <div className="bg-gray-50 dark:bg-dark-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <Activity className="mr-2 text-orange-500" />
              Memory Usage
            </h3>
            {performanceData?.memory ? (
              <div className="space-y-2">
                {Object.entries(performanceData.memory).map(([key, value]: [string, any]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">{key}:</span>
                    <span className="text-sm font-mono">
                      {value?.avg ? `${(value.avg / 1024 / 1024).toFixed(2)}MB` : 'N/A'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">Memory data not available</p>
            )}
          </div>
        </div>

        {/* Recent Errors */}
        {errorData?.recentErrors && errorData.recentErrors.length > 0 && (
          <div className="p-6 border-t border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold mb-3">Recent Errors</h3>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {errorData.recentErrors.map((error: any, index: number) => (
                <div key={index} className="bg-red-50 dark:bg-red-900/20 p-3 rounded text-sm">
                  <div className="font-semibold text-red-800 dark:text-red-200">
                    {error.error.message}
                  </div>
                  <div className="text-red-600 dark:text-red-400 text-xs mt-1">
                    {error.context.component} • {error.severity} • {new Date(error.context.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-between">
          <div className="flex space-x-2">
            <button
              onClick={() => errorTracker.clearErrors()}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Clear Errors
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Reload Page
            </button>
          </div>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default MonitoringDashboard;
