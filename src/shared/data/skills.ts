import { SkillCategory } from '../types';

export const skillCategories: SkillCategory[] = [
  {
    name: 'DevOps Tools',
    skills: [
      {
        name: '<PERSON>',
        icon: 'jenkins',
        level: 90,
      },
      {
        name: 'Docker',
        icon: 'docker',
        level: 95,
      },
      {
        name: 'Kubernet<PERSON>',
        icon: 'kubernetes',
        level: 85,
      },
      {
        name: 'Terraform',
        icon: 'terraform',
        level: 90,
      },
      {
        name: 'Ansible',
        icon: 'ansible',
        level: 80,
      },
      {
        name: 'Git',
        icon: 'git',
        level: 85,
      },
    ],
  },
  {
    name: 'Cloud Platforms',
    skills: [
      {
        name: 'A<PERSON>',
        icon: 'aws',
        level: 95,
      },
      {
        name: 'Azure',
        icon: 'azure',
        level: 75,
      },
      {
        name: 'G<PERSON>',
        icon: 'gcp',
        level: 70,
      },
      {
        name: 'AWS Lambda',
        icon: 'lambda',
        level: 85,
      },
      {
        name: 'AWS ECS',
        icon: 'ecs',
        level: 90,
      },
      {
        name: '<PERSON>WS EKS',
        icon: 'eks',
        level: 85,
      },
    ],
  },
  {
    name: 'Programming',
    skills: [
      {
        name: 'Python',
        icon: 'python',
        level: 85,
      },
      {
        name: 'Ba<PERSON>',
        icon: 'bash',
        level: 90,
      },
      {
        name: 'Go',
        icon: 'go',
        level: 70,
      },
      {
        name: 'JavaScript',
        icon: 'javascript',
        level: 75,
      },
      {
        name: 'YAML',
        icon: 'yaml',
        level: 95,
      },
    ],
  },
  {
    name: 'Infrastructure & Automation',
    skills: [
      {
        name: 'CI/CD',
        icon: 'cicd',
        level: 95,
      },
      {
        name: 'Infrastructure as Code',
        icon: 'iac',
        level: 90,
      },
      {
        name: 'Monitoring',
        icon: 'monitoring',
        level: 85,
      },
      {
        name: 'Linux Administration',
        icon: 'linux',
        level: 90,
      },
      {
        name: 'Cloud Architecture',
        icon: 'architecture',
        level: 85,
      },
    ],
  },
];