@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans bg-white dark:bg-dark-200 text-gray-800 dark:text-gray-100 transition-colors duration-300;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  h4 {
    @apply text-xl md:text-2xl;
  }

  p {
    @apply leading-relaxed;
  }

  section {
    @apply py-16 md:py-24;
  }
}

@layer components {
  .container {
    @apply px-4 mx-auto max-w-7xl;
  }
  
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-secondary-600 hover:bg-secondary-700 text-white focus:ring-secondary-500;
  }

  .btn-accent {
    @apply bg-accent-500 hover:bg-accent-600 text-white focus:ring-accent-400;
  }

  .btn-outline {
    @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900/20 focus:ring-primary-500;
  }

  .section-title {
    @apply text-3xl md:text-4xl font-bold mb-8 relative inline-block;
  }

  .section-title::after {
    @apply content-[''] absolute -bottom-2 left-0 w-1/3 h-1 bg-primary-600 rounded-full;
  }

  .card {
    @apply bg-white dark:bg-dark-100 rounded-xl shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden;
  }

  .nav-link {
    @apply text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium transition-colors duration-200;
  }

  .active-nav-link {
    @apply text-primary-600 dark:text-primary-400 font-semibold;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary-600 to-secondary-600;
  }
}