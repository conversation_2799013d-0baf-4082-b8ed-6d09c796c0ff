@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply dark:bg-dark-200 bg-white font-sans text-gray-800 transition-colors duration-300 dark:text-gray-100;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  h4 {
    @apply text-xl md:text-2xl;
  }

  p {
    @apply leading-relaxed;
  }

  section {
    @apply py-16 md:py-24;
  }
}

@layer components {
  .container {
    @apply mx-auto max-w-7xl px-4;
  }

  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-6 py-3 text-base font-medium transition duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 focus:ring-primary-500 text-white;
  }

  .btn-secondary {
    @apply bg-secondary-600 hover:bg-secondary-700 focus:ring-secondary-500 text-white;
  }

  .btn-accent {
    @apply bg-accent-500 hover:bg-accent-600 focus:ring-accent-400 text-white;
  }

  .btn-outline {
    @apply border-primary-600 text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900/20 focus:ring-primary-500 border-2;
  }

  .section-title {
    @apply relative mb-8 inline-block text-3xl font-bold md:text-4xl;
  }

  .section-title::after {
    @apply bg-primary-600 absolute -bottom-2 left-0 h-1 w-1/3 rounded-full content-[''];
  }

  .card {
    @apply dark:bg-dark-100 overflow-hidden rounded-xl bg-white shadow-md transition-shadow duration-300 hover:shadow-xl;
  }

  .nav-link {
    @apply hover:text-primary-600 dark:hover:text-primary-400 font-medium text-gray-700 transition-colors duration-200 dark:text-gray-300;
  }

  .active-nav-link {
    @apply text-primary-600 dark:text-primary-400 font-semibold;
  }
}

@layer utilities {
  .text-gradient {
    @apply from-primary-600 to-secondary-600 bg-gradient-to-r bg-clip-text text-transparent;
  }
}
